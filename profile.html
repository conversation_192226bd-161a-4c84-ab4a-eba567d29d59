<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .status-bar {
            height: 44px;
            background: #fff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
        }
        .tab-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 83px;
            background: #fff;
            border-top: 1px solid #e5e5e5;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
        }
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #8e8e93;
            font-size: 10px;
        }
        .tab-item.active {
            color: #007aff;
        }
        .tab-item i {
            font-size: 24px;
            margin-bottom: 4px;
        }
        .content-area {
            height: calc(100vh - 127px);
            overflow-y: auto;
            background: #f2f2f7;
            padding-bottom: 20px;
        }
        .menu-item {
            background: white;
            border-bottom: 1px solid #f0f0f0;
            padding: 16px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .menu-item:last-child {
            border-bottom: none;
        }
        .avatar {
            width: 80px;
            height: 80px;
            border-radius: 40px;
            object-fit: cover;
        }
        .stat-item {
            text-align: center;
            flex: 1;
        }
        .stat-number {
            font-size: 20px;
            font-weight: bold;
            color: #333;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- 状态栏 -->
    <div class="status-bar">
        <span>9:41</span>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
        <!-- 用户信息卡片 -->
        <div class="bg-white mx-4 mt-4 rounded-xl p-6 shadow-sm">
            <div class="flex items-center space-x-4 mb-4">
                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=160&h=160&fit=crop&crop=face" 
                     alt="头像" class="avatar">
                <div class="flex-1">
                    <h2 class="text-xl font-bold text-gray-900">管理员</h2>
                    <p class="text-gray-500 text-sm"><EMAIL></p>
                </div>
            </div>
            
            <!-- 统计数据 -->
            <div class="flex border-t pt-4">
                <div class="stat-item">
                    <div class="stat-number">128</div>
                    <div class="stat-label">已发布</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">2.4k</div>
                    <div class="stat-label">总下载</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">95%</div>
                    <div class="stat-label">好评率</div>
                </div>
            </div>
        </div>

        <!-- 功能菜单 -->
        <div class="bg-white mx-4 mt-4 rounded-xl shadow-sm overflow-hidden">
            <div class="menu-item">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-cloud-upload-alt text-blue-500"></i>
                    </div>
                    <span class="text-gray-900">上传新资源</span>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>
            
            <div class="menu-item">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-list text-green-500"></i>
                    </div>
                    <span class="text-gray-900">管理我的资源</span>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>
            
            <div class="menu-item">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-folder text-yellow-500"></i>
                    </div>
                    <span class="text-gray-900">内容审核</span>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>
        </div>

        <!-- 管理功能 -->
        <div class="bg-white mx-4 mt-4 rounded-xl shadow-sm overflow-hidden">
            <div class="menu-item">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-users text-purple-500"></i>
                    </div>
                    <span class="text-gray-900">用户管理</span>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>
            
            <div class="menu-item">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-cog text-gray-500"></i>
                    </div>
                    <span class="text-gray-900">设置</span>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>
        </div>

        <!-- 退出登录 -->
        <div class="mx-4 mt-6">
            <button class="w-full bg-red-50 text-red-500 py-3 rounded-xl font-medium">
                退出登录
            </button>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="tab-bar">
        <div class="tab-item">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-search"></i>
            <span>搜索</span>
        </div>
        <div class="tab-item active">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html>
