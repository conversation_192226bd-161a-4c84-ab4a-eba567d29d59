<template>
	<view class="profile-page">
		<!-- 自定义状态栏 -->
		<view class="status-bar" :style="{height: statusBarHeight + 'px'}"></view>
		
		<!-- 用户信息卡片 -->
		<view class="user-card">
			<view class="user-info">
				<image class="avatar" :src="userInfo.avatar" mode="aspectFill"></image>
				<view class="user-details">
					<view class="username">{{userInfo.nickname}}</view>
					<view class="user-email">{{userInfo.email}}</view>
				</view>
			</view>
			
			<!-- 统计数据 -->
			<view class="stats-row">
				<view class="stat-item">
					<view class="stat-number">{{userStats.published}}</view>
					<view class="stat-label">已发布</view>
				</view>
				<view class="stat-item">
					<view class="stat-number">{{userStats.downloads}}</view>
					<view class="stat-label">总下载</view>
				</view>
				<view class="stat-item">
					<view class="stat-number">{{userStats.rating}}</view>
					<view class="stat-label">好评率</view>
				</view>
			</view>
		</view>

		<!-- 功能菜单 -->
		<view class="menu-section">
			<view class="menu-item" @click="goToUpload">
				<view class="menu-left">
					<view class="menu-icon upload">📤</view>
					<text class="menu-text">分享新资源</text>
				</view>
				<text class="menu-arrow">></text>
			</view>
			
			<view class="menu-item" @click="goToMyResources">
				<view class="menu-left">
					<view class="menu-icon manage">📋</view>
					<text class="menu-text">管理我的资源</text>
				</view>
				<text class="menu-arrow">></text>
			</view>
			
			<view class="menu-item" @click="goToFavorites">
				<view class="menu-left">
					<view class="menu-icon favorite">❤️</view>
					<text class="menu-text">我的收藏</text>
				</view>
				<text class="menu-arrow">></text>
			</view>
		</view>

		<!-- 管理功能 -->
		<view class="menu-section" v-if="userInfo.isAdmin">
			<view class="menu-item" @click="goToUserManage">
				<view class="menu-left">
					<view class="menu-icon users">👥</view>
					<text class="menu-text">用户管理</text>
				</view>
				<text class="menu-arrow">></text>
			</view>
			
			<view class="menu-item" @click="goToAudit">
				<view class="menu-left">
					<view class="menu-icon audit">🔍</view>
					<text class="menu-text">内容审核</text>
				</view>
				<text class="menu-arrow">></text>
			</view>
		</view>

		<!-- 设置菜单 -->
		<view class="menu-section">
			<view class="menu-item" @click="goToSettings">
				<view class="menu-left">
					<view class="menu-icon settings">⚙️</view>
					<text class="menu-text">设置</text>
				</view>
				<text class="menu-arrow">></text>
			</view>
			
			<view class="menu-item" @click="goToAbout">
				<view class="menu-left">
					<view class="menu-icon about">ℹ️</view>
					<text class="menu-text">关于我们</text>
				</view>
				<text class="menu-arrow">></text>
			</view>
		</view>

		<!-- 退出登录 -->
		<view class="logout-section">
			<button class="logout-btn" @click="logout">退出登录</button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			statusBarHeight: 0,
			userInfo: {
				nickname: '管理员',
				email: '<EMAIL>',
				avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=160&h=160&fit=crop&crop=face',
				isAdmin: true
			},
			userStats: {
				published: '128',
				downloads: '2.4k',
				rating: '95%'
			}
		}
	},
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight;
		
		// 加载用户信息
		this.loadUserInfo();
	},
	onShow() {
		// 页面显示时刷新数据
		this.loadUserStats();
	},
	methods: {
		loadUserInfo() {
			// 从缓存或API获取用户信息
			const userInfo = uni.getStorageSync('userInfo');
			if (userInfo) {
				this.userInfo = userInfo;
			}
		},
		
		loadUserStats() {
			// 加载用户统计数据
			// TODO: 调用API获取真实数据
		},
		
		goToUpload() {
			uni.navigateTo({
				url: '/pages/upload/upload'
			});
		},
		
		goToMyResources() {
			uni.showToast({
				title: '功能开发中',
				icon: 'none'
			});
		},
		
		goToFavorites() {
			uni.showToast({
				title: '功能开发中',
				icon: 'none'
			});
		},
		
		goToUserManage() {
			uni.showToast({
				title: '功能开发中',
				icon: 'none'
			});
		},
		
		goToAudit() {
			uni.showToast({
				title: '功能开发中',
				icon: 'none'
			});
		},
		
		goToSettings() {
			uni.showToast({
				title: '功能开发中',
				icon: 'none'
			});
		},
		
		goToAbout() {
			uni.showToast({
				title: '功能开发中',
				icon: 'none'
			});
		},
		
		logout() {
			uni.showModal({
				title: '确认退出',
				content: '确定要退出登录吗？',
				success: (res) => {
					if (res.confirm) {
						// 清除用户信息
						uni.removeStorageSync('userInfo');
						uni.removeStorageSync('token');
						
						// 跳转到登录页
						uni.reLaunch({
							url: '/pages/login/login'
						});
					}
				}
			});
		}
	}
}
</script>

<style scoped>
.profile-page {
	background-color: #f2f2f7;
	min-height: 100vh;
}

.user-card {
	background-color: #ffffff;
	margin: 32rpx;
	border-radius: 24rpx;
	padding: 48rpx;
	box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.user-info {
	display: flex;
	align-items: center;
	margin-bottom: 32rpx;
}

.avatar {
	width: 160rpx;
	height: 160rpx;
	border-radius: 80rpx;
	margin-right: 32rpx;
}

.user-details {
	flex: 1;
}

.username {
	font-size: 40rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}

.user-email {
	font-size: 28rpx;
	color: #666;
}

.stats-row {
	display: flex;
	border-top: 1rpx solid #f0f0f0;
	padding-top: 32rpx;
}

.stat-item {
	flex: 1;
	text-align: center;
}

.stat-number {
	font-size: 40rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}

.stat-label {
	font-size: 24rpx;
	color: #666;
}

.menu-section {
	background-color: #ffffff;
	margin: 32rpx;
	border-radius: 24rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.menu-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 32rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.menu-item:last-child {
	border-bottom: none;
}

.menu-left {
	display: flex;
	align-items: center;
}

.menu-icon {
	width: 64rpx;
	height: 64rpx;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	margin-right: 24rpx;
}

.menu-icon.upload {
	background-color: #e3f2fd;
}

.menu-icon.manage {
	background-color: #e8f5e8;
}

.menu-icon.favorite {
	background-color: #fce4ec;
}

.menu-icon.users {
	background-color: #f3e5f5;
}

.menu-icon.audit {
	background-color: #fff3e0;
}

.menu-icon.settings {
	background-color: #f5f5f5;
}

.menu-icon.about {
	background-color: #e0f2f1;
}

.menu-text {
	font-size: 32rpx;
	color: #333;
}

.menu-arrow {
	font-size: 32rpx;
	color: #ccc;
}

.logout-section {
	margin: 48rpx 32rpx 32rpx;
}

.logout-btn {
	width: 100%;
	height: 88rpx;
	background-color: #ff3b30;
	color: #ffffff;
	border: none;
	border-radius: 24rpx;
	font-size: 32rpx;
	font-weight: 500;
}

.logout-btn::after {
	border: none;
}
</style>
