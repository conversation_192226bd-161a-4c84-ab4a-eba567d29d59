<template>
	<view class="login-page">
		<!-- 自定义状态栏 -->
		<view class="status-bar" :style="{height: statusBarHeight + 'px'}"></view>
		
		<!-- 登录内容 -->
		<view class="login-content">
			<!-- Logo和标题 -->
			<view class="logo-section">
				<view class="logo">📚</view>
				<view class="app-name">资源分享</view>
				<view class="app-desc">发现、分享、创造</view>
			</view>

			<!-- 登录表单 -->
			<view class="login-form">
				<view class="form-group">
					<view class="input-wrapper">
						<text class="input-icon">📧</text>
						<input 
							class="form-input" 
							type="text" 
							placeholder="请输入邮箱或手机号" 
							v-model="loginForm.username"
						/>
					</view>
				</view>

				<view class="form-group">
					<view class="input-wrapper">
						<text class="input-icon">🔒</text>
						<input 
							class="form-input" 
							:type="showPassword ? 'text' : 'password'" 
							placeholder="请输入密码" 
							v-model="loginForm.password"
						/>
						<text class="toggle-password" @click="togglePasswordVisibility">
							{{showPassword ? '👁️' : '👁️‍🗨️'}}
						</text>
					</view>
				</view>

				<view class="form-options">
					<view class="remember-row">
						<checkbox 
							:checked="rememberMe" 
							@click="toggleRemember"
							color="#007aff"
						/>
						<text class="remember-text">记住密码</text>
					</view>
					<text class="forgot-password" @click="forgotPassword">忘记密码？</text>
				</view>

				<button class="login-btn" @click="handleLogin" :disabled="!canLogin">
					登录
				</button>

				<view class="register-row">
					<text class="register-text">还没有账号？</text>
					<text class="register-link" @click="goToRegister">立即注册</text>
				</view>
			</view>

			<!-- 第三方登录 -->
			<view class="third-party-login">
				<view class="divider">
					<view class="divider-line"></view>
					<text class="divider-text">其他登录方式</text>
					<view class="divider-line"></view>
				</view>

				<view class="third-party-buttons">
					<button class="third-party-btn wechat" @click="wechatLogin">
						<text class="btn-icon">💬</text>
						<text class="btn-text">微信登录</text>
					</button>
					
					<button class="third-party-btn qq" @click="qqLogin">
						<text class="btn-icon">🐧</text>
						<text class="btn-text">QQ登录</text>
					</button>
				</view>
			</view>
		</view>

		<!-- 底部协议 -->
		<view class="bottom-agreement">
			<text class="agreement-text">
				登录即表示同意
				<text class="agreement-link">《用户协议》</text>
				和
				<text class="agreement-link">《隐私政策》</text>
			</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			statusBarHeight: 0,
			showPassword: false,
			rememberMe: false,
			loginForm: {
				username: '',
				password: ''
			}
		}
	},
	computed: {
		canLogin() {
			return this.loginForm.username && this.loginForm.password;
		}
	},
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight;
		
		// 检查是否已登录
		this.checkLoginStatus();
	},
	methods: {
		checkLoginStatus() {
			const token = uni.getStorageSync('token');
			if (token) {
				// 已登录，跳转到首页
				uni.reLaunch({
					url: '/pages/index/index'
				});
			}
		},
		
		togglePasswordVisibility() {
			this.showPassword = !this.showPassword;
		},
		
		toggleRemember() {
			this.rememberMe = !this.rememberMe;
		},
		
		handleLogin() {
			if (!this.canLogin) {
				uni.showToast({
					title: '请填写完整信息',
					icon: 'none'
				});
				return;
			}
			
			uni.showLoading({
				title: '登录中...'
			});
			
			// TODO: 调用登录API
			setTimeout(() => {
				uni.hideLoading();
				
				// 模拟登录成功
				const userInfo = {
					id: 1,
					nickname: '管理员',
					email: this.loginForm.username,
					avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=160&h=160&fit=crop&crop=face',
					isAdmin: true
				};
				
				// 保存用户信息
				uni.setStorageSync('token', 'mock_token_123456');
				uni.setStorageSync('userInfo', userInfo);
				
				if (this.rememberMe) {
					uni.setStorageSync('savedPassword', this.loginForm.password);
				}
				
				uni.showToast({
					title: '登录成功',
					icon: 'success'
				});
				
				// 跳转到首页
				setTimeout(() => {
					uni.reLaunch({
						url: '/pages/index/index'
					});
				}, 1500);
			}, 2000);
		},
		
		wechatLogin() {
			uni.showToast({
				title: '微信登录开发中',
				icon: 'none'
			});
		},
		
		qqLogin() {
			uni.showToast({
				title: 'QQ登录开发中',
				icon: 'none'
			});
		},
		
		goToRegister() {
			uni.showToast({
				title: '注册功能开发中',
				icon: 'none'
			});
		},
		
		forgotPassword() {
			uni.showToast({
				title: '找回密码功能开发中',
				icon: 'none'
			});
		}
	}
}
</script>

<style scoped>
.login-page {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	min-height: 100vh;
	display: flex;
	flex-direction: column;
}

.login-content {
	flex: 1;
	padding: 80rpx 48rpx 48rpx;
	display: flex;
	flex-direction: column;
	justify-content: center;
}

.logo-section {
	text-align: center;
	margin-bottom: 80rpx;
}

.logo {
	font-size: 120rpx;
	margin-bottom: 24rpx;
}

.app-name {
	font-size: 48rpx;
	font-weight: bold;
	color: #ffffff;
	margin-bottom: 16rpx;
}

.app-desc {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.8);
}

.login-form {
	background-color: #ffffff;
	border-radius: 32rpx;
	padding: 48rpx;
	margin-bottom: 48rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.form-group {
	margin-bottom: 32rpx;
}

.input-wrapper {
	position: relative;
	display: flex;
	align-items: center;
	background-color: #f8f9fa;
	border-radius: 16rpx;
	padding: 0 24rpx;
}

.input-icon {
	font-size: 32rpx;
	margin-right: 16rpx;
	color: #666;
}

.form-input {
	flex: 1;
	height: 88rpx;
	font-size: 28rpx;
	background-color: transparent;
	border: none;
}

.toggle-password {
	font-size: 32rpx;
	color: #666;
	padding: 16rpx;
}

.form-options {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 48rpx;
}

.remember-row {
	display: flex;
	align-items: center;
}

.remember-text {
	font-size: 24rpx;
	color: #666;
	margin-left: 16rpx;
}

.forgot-password {
	font-size: 24rpx;
	color: #007aff;
}

.login-btn {
	width: 100%;
	height: 88rpx;
	background-color: #007aff;
	color: #ffffff;
	border: none;
	border-radius: 16rpx;
	font-size: 32rpx;
	font-weight: 600;
	margin-bottom: 32rpx;
}

.login-btn:disabled {
	background-color: #ccc;
}

.login-btn::after {
	border: none;
}

.register-row {
	text-align: center;
}

.register-text {
	font-size: 24rpx;
	color: #666;
}

.register-link {
	font-size: 24rpx;
	color: #007aff;
	margin-left: 8rpx;
}

.third-party-login {
	background-color: #ffffff;
	border-radius: 32rpx;
	padding: 48rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.divider {
	display: flex;
	align-items: center;
	margin-bottom: 32rpx;
}

.divider-line {
	flex: 1;
	height: 1rpx;
	background-color: #e5e5e5;
}

.divider-text {
	font-size: 24rpx;
	color: #666;
	margin: 0 24rpx;
}

.third-party-buttons {
	display: flex;
	gap: 24rpx;
}

.third-party-btn {
	flex: 1;
	height: 88rpx;
	border: 2rpx solid #e5e5e5;
	border-radius: 16rpx;
	background-color: #ffffff;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
}

.third-party-btn::after {
	border: none;
}

.third-party-btn.wechat {
	color: #1aad19;
	border-color: #1aad19;
}

.third-party-btn.qq {
	color: #12b7f5;
	border-color: #12b7f5;
}

.btn-icon {
	margin-right: 8rpx;
	font-size: 32rpx;
}

.bottom-agreement {
	padding: 32rpx 48rpx;
	text-align: center;
}

.agreement-text {
	font-size: 20rpx;
	color: rgba(255, 255, 255, 0.8);
	line-height: 1.6;
}

.agreement-link {
	color: #ffffff;
	text-decoration: underline;
}
</style>
