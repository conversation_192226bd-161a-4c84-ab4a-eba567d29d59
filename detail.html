<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资源详情</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .status-bar {
            height: 44px;
            background: #fff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
        }
        .content-area {
            height: calc(100vh - 44px);
            overflow-y: auto;
            background: #f2f2f7;
            padding-bottom: 100px;
        }
        .preview-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 12px;
        }
        .tag {
            display: inline-block;
            background: #f0f0f0;
            color: #666;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-right: 8px;
            margin-bottom: 8px;
        }
        .download-btn {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: #007aff;
            color: white;
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(0,122,255,0.3);
        }
        .author-avatar {
            width: 40px;
            height: 40px;
            border-radius: 20px;
            object-fit: cover;
        }
        .rating-stars {
            color: #ffd700;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- 状态栏 -->
    <div class="status-bar">
        <div class="flex items-center space-x-3">
            <i class="fas fa-arrow-left text-lg"></i>
            <span>资源详情</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
        <!-- 资源预览图 -->
        <div class="px-4 pt-4">
            <img src="https://images.unsplash.com/photo-1561070791-2526d30994b5?w=400&h=200&fit=crop" 
                 alt="UI Kit预览" class="preview-image">
        </div>

        <!-- 资源信息 -->
        <div class="bg-white mx-4 mt-4 rounded-xl p-4 shadow-sm">
            <div class="flex items-start justify-between mb-3">
                <div class="flex-1">
                    <h1 class="text-xl font-bold text-gray-900 mb-2">红文娱乐 UI Kit</h1>
                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                        <span><i class="fas fa-download mr-1"></i>1.8万下载</span>
                        <span><i class="fas fa-eye mr-1"></i>5.2万浏览</span>
                        <span><i class="fas fa-heart mr-1"></i>3.1k喜欢</span>
                    </div>
                </div>
                <span class="bg-blue-100 text-blue-800 text-xs px-3 py-1 rounded-full">UI设计</span>
            </div>

            <!-- 评分 -->
            <div class="flex items-center space-x-2 mb-4">
                <div class="rating-stars">
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star-half-alt"></i>
                </div>
                <span class="text-sm text-gray-600">4.8 (326 评价)</span>
            </div>

            <!-- 作者信息 -->
            <div class="flex items-center space-x-3 py-3 border-t border-gray-100">
                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&crop=face" 
                     alt="作者头像" class="author-avatar">
                <div class="flex-1">
                    <h3 class="font-semibold text-gray-900">Creative Studio</h3>
                    <p class="text-sm text-gray-500">专业UI设计团队</p>
                </div>
                <button class="bg-blue-500 text-white px-4 py-2 rounded-lg text-sm">关注</button>
            </div>
        </div>

        <!-- 资源描述 -->
        <div class="bg-white mx-4 mt-4 rounded-xl p-4 shadow-sm">
            <h2 class="text-lg font-semibold text-gray-900 mb-3">资源描述</h2>
            <p class="text-gray-600 text-sm leading-relaxed mb-4">
                这是一套完整的娱乐类应用UI组件设计，包含了登录注册、首页、个人中心、设置等常用页面的设计稿。
                设计风格现代简洁，色彩搭配和谐，适合各种娱乐类移动应用。
            </p>
            
            <h3 class="font-semibold text-gray-900 mb-2">包含内容：</h3>
            <ul class="text-sm text-gray-600 space-y-1 mb-4">
                <li>• 20+ 精美页面设计</li>
                <li>• 100+ UI组件</li>
                <li>• Sketch & Figma 源文件</li>
                <li>• 设计规范文档</li>
                <li>• 图标字体文件</li>
            </ul>

            <!-- 标签 -->
            <div class="mb-4">
                <h3 class="font-semibold text-gray-900 mb-2">标签：</h3>
                <div>
                    <span class="tag">UI设计</span>
                    <span class="tag">移动端</span>
                    <span class="tag">娱乐</span>
                    <span class="tag">Sketch</span>
                    <span class="tag">Figma</span>
                </div>
            </div>
        </div>

        <!-- 文件信息 -->
        <div class="bg-white mx-4 mt-4 rounded-xl p-4 shadow-sm">
            <h2 class="text-lg font-semibold text-gray-900 mb-3">文件信息</h2>
            <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                    <span class="text-gray-600">文件大小</span>
                    <span class="text-gray-900">45.2 MB</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">文件格式</span>
                    <span class="text-gray-900">Sketch, Figma, PNG</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">更新时间</span>
                    <span class="text-gray-900">2024-01-15</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">兼容版本</span>
                    <span class="text-gray-900">Sketch 70+, Figma</span>
                </div>
            </div>
        </div>

        <!-- 相关推荐 -->
        <div class="bg-white mx-4 mt-4 rounded-xl p-4 shadow-sm">
            <h2 class="text-lg font-semibold text-gray-900 mb-3">相关推荐</h2>
            <div class="space-y-3">
                <div class="flex items-center space-x-3">
                    <img src="https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=60&h=60&fit=crop" 
                         alt="相关资源" class="w-12 h-12 rounded-lg object-cover">
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-900 text-sm">电商APP UI Kit</h4>
                        <p class="text-xs text-gray-500">完整的电商应用界面设计</p>
                    </div>
                    <span class="text-xs text-blue-500">查看</span>
                </div>
                
                <div class="flex items-center space-x-3">
                    <img src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=60&h=60&fit=crop" 
                         alt="相关资源" class="w-12 h-12 rounded-lg object-cover">
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-900 text-sm">社交APP界面</h4>
                        <p class="text-xs text-gray-500">现代化社交应用设计模板</p>
                    </div>
                    <span class="text-xs text-blue-500">查看</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 下载按钮 -->
    <div class="download-btn">
        <i class="fas fa-download mr-2"></i>
        立即下载
    </div>
</body>
</html>
