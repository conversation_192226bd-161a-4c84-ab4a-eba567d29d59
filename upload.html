<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上传资源</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .status-bar {
            height: 44px;
            background: #fff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
        }
        .content-area {
            height: calc(100vh - 44px);
            overflow-y: auto;
            background: #f2f2f7;
            padding-bottom: 100px;
        }
        .upload-area {
            border: 2px dashed #d1d5db;
            border-radius: 12px;
            padding: 40px 20px;
            text-align: center;
            background: #f9fafb;
            margin-bottom: 20px;
        }
        .upload-area.dragover {
            border-color: #3b82f6;
            background: #eff6ff;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-label {
            display: block;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            font-size: 14px;
        }
        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            background: white;
        }
        .form-textarea {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            background: white;
            min-height: 100px;
            resize: vertical;
        }
        .category-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }
        .category-item {
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
            background: white;
        }
        .category-item.selected {
            border-color: #3b82f6;
            background: #eff6ff;
            color: #1d4ed8;
        }
        .submit-btn {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: #007aff;
            color: white;
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(0,122,255,0.3);
            border: none;
        }
        .file-preview {
            display: flex;
            align-items: center;
            background: white;
            padding: 12px;
            border-radius: 8px;
            margin-top: 12px;
            border: 1px solid #e5e7eb;
        }
        .file-icon {
            width: 40px;
            height: 40px;
            background: #f3f4f6;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- 状态栏 -->
    <div class="status-bar">
        <div class="flex items-center space-x-3">
            <i class="fas fa-arrow-left text-lg"></i>
            <span>上传资源</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
        <form class="px-4 pt-4">
            <!-- 文件上传区域 -->
            <div class="bg-white rounded-xl p-4 shadow-sm mb-4">
                <label class="form-label">上传文件 *</label>
                <div class="upload-area" id="uploadArea">
                    <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-3"></i>
                    <p class="text-gray-600 mb-2">点击或拖拽文件到此处上传</p>
                    <p class="text-sm text-gray-400">支持 ZIP, RAR, Sketch, Figma, PSD 等格式</p>
                    <p class="text-sm text-gray-400">最大文件大小: 100MB</p>
                </div>
                
                <!-- 文件预览 -->
                <div id="filePreview" class="hidden">
                    <div class="file-preview">
                        <div class="file-icon">
                            <i class="fas fa-file-archive text-blue-500"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-900 text-sm">UI-Kit-Design.zip</p>
                            <p class="text-xs text-gray-500">45.2 MB</p>
                        </div>
                        <i class="fas fa-times text-gray-400 cursor-pointer"></i>
                    </div>
                </div>
            </div>

            <!-- 基本信息 -->
            <div class="bg-white rounded-xl p-4 shadow-sm mb-4">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">基本信息</h2>
                
                <div class="form-group">
                    <label class="form-label">资源标题 *</label>
                    <input type="text" class="form-input" placeholder="请输入资源标题" 
                           value="红文娱乐 UI Kit">
                </div>

                <div class="form-group">
                    <label class="form-label">资源描述 *</label>
                    <textarea class="form-textarea" placeholder="详细描述您的资源内容、特点和用途">这是一套完整的娱乐类应用UI组件设计，包含了登录注册、首页、个人中心、设置等常用页面的设计稿。设计风格现代简洁，色彩搭配和谐，适合各种娱乐类移动应用。</textarea>
                </div>

                <div class="form-group">
                    <label class="form-label">资源分类 *</label>
                    <div class="category-grid">
                        <div class="category-item selected">
                            <i class="fas fa-palette text-lg mb-2"></i>
                            <p class="text-sm">UI设计</p>
                        </div>
                        <div class="category-item">
                            <i class="fas fa-code text-lg mb-2"></i>
                            <p class="text-sm">开发工具</p>
                        </div>
                        <div class="category-item">
                            <i class="fas fa-image text-lg mb-2"></i>
                            <p class="text-sm">图片素材</p>
                        </div>
                        <div class="category-item">
                            <i class="fas fa-music text-lg mb-2"></i>
                            <p class="text-sm">音频素材</p>
                        </div>
                        <div class="category-item">
                            <i class="fas fa-font text-lg mb-2"></i>
                            <p class="text-sm">字体文件</p>
                        </div>
                        <div class="category-item">
                            <i class="fas fa-file-alt text-lg mb-2"></i>
                            <p class="text-sm">文档模板</p>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">标签</label>
                    <input type="text" class="form-input" placeholder="用逗号分隔多个标签" 
                           value="UI设计, 移动端, 娱乐, Sketch, Figma">
                </div>
            </div>

            <!-- 详细信息 -->
            <div class="bg-white rounded-xl p-4 shadow-sm mb-4">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">详细信息</h2>
                
                <div class="form-group">
                    <label class="form-label">兼容软件</label>
                    <input type="text" class="form-input" placeholder="如: Sketch 70+, Figma, Photoshop CC" 
                           value="Sketch 70+, Figma">
                </div>

                <div class="form-group">
                    <label class="form-label">文件格式</label>
                    <input type="text" class="form-input" placeholder="如: Sketch, Figma, PSD, PNG" 
                           value="Sketch, Figma, PNG">
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div class="form-group">
                        <label class="form-label">资源类型</label>
                        <select class="form-input">
                            <option>免费资源</option>
                            <option>付费资源</option>
                            <option>会员专享</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">价格 (元)</label>
                        <input type="number" class="form-input" placeholder="0" value="0">
                    </div>
                </div>
            </div>

            <!-- 预览图片 -->
            <div class="bg-white rounded-xl p-4 shadow-sm mb-4">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">预览图片</h2>
                <div class="upload-area">
                    <i class="fas fa-image text-3xl text-gray-400 mb-3"></i>
                    <p class="text-gray-600 mb-2">上传资源预览图</p>
                    <p class="text-sm text-gray-400">建议尺寸: 800x600px, 支持 JPG, PNG 格式</p>
                </div>
            </div>

            <!-- 使用协议 -->
            <div class="bg-white rounded-xl p-4 shadow-sm mb-4">
                <div class="flex items-start space-x-3">
                    <input type="checkbox" id="agreement" class="mt-1" checked>
                    <label for="agreement" class="text-sm text-gray-600 leading-relaxed">
                        我确认拥有此资源的版权或使用权，同意遵守平台的
                        <span class="text-blue-500">《资源分享协议》</span>
                        和
                        <span class="text-blue-500">《版权声明》</span>
                    </label>
                </div>
            </div>
        </form>
    </div>

    <!-- 提交按钮 -->
    <button class="submit-btn">
        <i class="fas fa-upload mr-2"></i>
        发布资源
    </button>

    <script>
        // 分类选择
        document.querySelectorAll('.category-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.category-item').forEach(i => i.classList.remove('selected'));
                this.classList.add('selected');
            });
        });

        // 文件上传区域交互
        const uploadArea = document.getElementById('uploadArea');
        const filePreview = document.getElementById('filePreview');

        uploadArea.addEventListener('click', function() {
            // 模拟文件选择
            filePreview.classList.remove('hidden');
            uploadArea.style.display = 'none';
        });

        // 删除文件
        document.querySelector('.fa-times').addEventListener('click', function() {
            filePreview.classList.add('hidden');
            uploadArea.style.display = 'block';
        });
    </script>
</body>
</html>
