<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资源分享小程序 - 高保真原型</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .phone-container {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            margin: 20px;
        }
        .phone-screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .notch {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 30px;
            background: #000;
            border-radius: 0 0 20px 20px;
            z-index: 10;
        }
        .prototype-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 40px;
            padding: 40px;
            background: #f8fafc;
            min-height: 100vh;
        }
        .prototype-title {
            text-align: center;
            font-size: 2rem;
            font-weight: bold;
            color: #1e293b;
            margin-bottom: 40px;
            grid-column: 1 / -1;
        }
        .screen-title {
            text-align: center;
            font-size: 1.2rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 10px;
        }
        iframe {
            border: none;
            width: 100%;
            height: 100%;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="prototype-title">资源分享小程序 - 高保真原型展示</div>
    
    <div class="prototype-grid">
        <!-- 首页 -->
        <div>
            <div class="screen-title">1. 首页 (Home)</div>
            <div class="phone-container">
                <div class="phone-screen">
                    <div class="notch"></div>
                    <iframe src="home.html"></iframe>
                </div>
            </div>
        </div>

        <!-- 搜索页 -->
        <div>
            <div class="screen-title">2. 搜索 (Search)</div>
            <div class="phone-container">
                <div class="phone-screen">
                    <div class="notch"></div>
                    <iframe src="search.html"></iframe>
                </div>
            </div>
        </div>

        <!-- 个人中心 -->
        <div>
            <div class="screen-title">3. 我的 (Profile)</div>
            <div class="phone-container">
                <div class="phone-screen">
                    <div class="notch"></div>
                    <iframe src="profile.html"></iframe>
                </div>
            </div>
        </div>

        <!-- 资源详情页 -->
        <div>
            <div class="screen-title">4. 资源详情 (Detail)</div>
            <div class="phone-container">
                <div class="phone-screen">
                    <div class="notch"></div>
                    <iframe src="detail.html"></iframe>
                </div>
            </div>
        </div>

        <!-- 上传资源页 -->
        <div>
            <div class="screen-title">5. 上传资源 (Upload)</div>
            <div class="phone-container">
                <div class="phone-screen">
                    <div class="notch"></div>
                    <iframe src="upload.html"></iframe>
                </div>
            </div>
        </div>

        <!-- 分类页面 -->
        <div>
            <div class="screen-title">6. 分类浏览 (Category)</div>
            <div class="phone-container">
                <div class="phone-screen">
                    <div class="notch"></div>
                    <iframe src="category.html"></iframe>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
