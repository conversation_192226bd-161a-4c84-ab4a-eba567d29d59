{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "首页",
				"navigationStyle": "custom"
			}
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "white",
		"navigationBarTitleText": "千木软件库",
		"navigationBarBackgroundColor": "#000000",
		"backgroundColor": "red",
		"enablePullDownRefresh": true,
		"onReachBottomDistance": 50
	},
	"uniIdRouter": {},
	"tabBar": {
		"color": "#7A7E83",
		"selectedColor": "#1E90FF",
		"backgroundColor": "#FFFFFF",
		"position": "bottom",
		"borderStyle": "black",
		"list": [
			{
				"pagePath": "pages/index/index",
				"text": "软库"
			}
		]
	}
	
}
