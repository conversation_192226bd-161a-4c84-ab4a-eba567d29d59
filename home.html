<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .status-bar {
            height: 44px;
            background: #fff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
        }
        .tab-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 83px;
            background: #fff;
            border-top: 1px solid #e5e5e5;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
        }
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #8e8e93;
            font-size: 10px;
        }
        .tab-item.active {
            color: #007aff;
        }
        .tab-item i {
            font-size: 24px;
            margin-bottom: 4px;
        }
        .content-area {
            height: calc(100vh - 127px);
            overflow-y: auto;
            background: #f2f2f7;
            padding-bottom: 20px;
        }
        .category-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin-bottom: 8px;
        }
        .resource-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .resource-image {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            object-fit: cover;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- 状态栏 -->
    <div class="status-bar">
        <span>9:41</span>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
        <!-- 头部 -->
        <div class="bg-white px-4 py-6">
            <h1 class="text-2xl font-bold text-gray-900 mb-2">资源中心</h1>
            <p class="text-gray-500 text-sm">发现、分享、创造</p>
            
            <!-- 搜索框 -->
            <div class="mt-4 relative">
                <input type="text" placeholder="搜索设计资源、代码模板..." 
                       class="w-full bg-gray-100 rounded-lg px-4 py-3 pl-10 text-sm">
                <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            </div>
        </div>

        <!-- 分类浏览 -->
        <div class="px-4 mt-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">分类浏览</h2>
            <div class="grid grid-cols-4 gap-4">
                <div class="text-center">
                    <div class="category-icon bg-blue-500">
                        <i class="fas fa-palette"></i>
                    </div>
                    <span class="text-xs text-gray-600">UI设计</span>
                </div>
                <div class="text-center">
                    <div class="category-icon bg-green-500">
                        <i class="fas fa-code"></i>
                    </div>
                    <span class="text-xs text-gray-600">开发</span>
                </div>
                <div class="text-center">
                    <div class="category-icon bg-orange-500">
                        <i class="fas fa-image"></i>
                    </div>
                    <span class="text-xs text-gray-600">素材</span>
                </div>
                <div class="text-center">
                    <div class="category-icon bg-purple-500">
                        <i class="fas fa-music"></i>
                    </div>
                    <span class="text-xs text-gray-600">音频</span>
                </div>
            </div>
        </div>

        <!-- 精选推荐 -->
        <div class="px-4 mt-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold text-gray-900">精选推荐</h2>
                <span class="text-blue-500 text-sm">查看全部</span>
            </div>

            <!-- 推荐资源列表 -->
            <div class="space-y-3">
                <div class="resource-card">
                    <div class="flex items-center space-x-3">
                        <img src="https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=120&h=120&fit=crop" 
                             alt="Python" class="resource-image">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-1">
                                <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">开发</span>
                            </div>
                            <h3 class="font-semibold text-gray-900 text-sm">Python 自动化脚本</h3>
                            <p class="text-gray-500 text-xs mt-1">一套完整的Python爬虫，提高工作效率</p>
                            <div class="flex items-center mt-2 text-xs text-gray-400">
                                <i class="fas fa-user mr-1"></i>
                                <span class="mr-3">设计师小王</span>
                                <i class="fas fa-download mr-1"></i>
                                <span>2.5万</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="resource-card">
                    <div class="flex items-center space-x-3">
                        <img src="https://images.unsplash.com/photo-1561070791-2526d30994b5?w=120&h=120&fit=crop" 
                             alt="UI Kit" class="resource-image">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-1">
                                <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">UI设计</span>
                            </div>
                            <h3 class="font-semibold text-gray-900 text-sm">红文娱乐 UI Kit</h3>
                            <p class="text-gray-500 text-xs mt-1">完整的娱乐类应用UI组件设计</p>
                            <div class="flex items-center mt-2 text-xs text-gray-400">
                                <i class="fas fa-user mr-1"></i>
                                <span class="mr-3">Creative Studio</span>
                                <i class="fas fa-download mr-1"></i>
                                <span>1.8万</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="resource-card">
                    <div class="flex items-center space-x-3">
                        <img src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=120&h=120&fit=crop" 
                             alt="无版权音乐" class="resource-image">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-1">
                                <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded">音频</span>
                            </div>
                            <h3 class="font-semibold text-gray-900 text-sm">无版权背景音乐包</h3>
                            <p class="text-gray-500 text-xs mt-1">50首高质量无版权背景音乐</p>
                            <div class="flex items-center mt-2 text-xs text-gray-400">
                                <i class="fas fa-user mr-1"></i>
                                <span class="mr-3">音乐工作室</span>
                                <i class="fas fa-download mr-1"></i>
                                <span>3.2万</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="tab-bar">
        <div class="tab-item active">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-search"></i>
            <span>搜索</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html>
