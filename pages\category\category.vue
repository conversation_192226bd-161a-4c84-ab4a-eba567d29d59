<template>
	<view class="category-page">
		<!-- 头部 -->
		<view class="header">
			<view class="header-title">分类浏览</view>
			<view class="header-desc">探索不同类型的优质资源</view>
		</view>

		<!-- 统计信息 -->
		<view class="stats-card">
			<view class="stat-item">
				<view class="stat-number">{{stats.totalResources}}</view>
				<view class="stat-label">总资源</view>
			</view>
			<view class="stat-item">
				<view class="stat-number">{{stats.todayNew}}</view>
				<view class="stat-label">今日新增</view>
			</view>
			<view class="stat-item">
				<view class="stat-number">{{stats.totalDownloads}}</view>
				<view class="stat-label">总下载</view>
			</view>
			<view class="stat-item">
				<view class="stat-number">{{stats.activeUsers}}</view>
				<view class="stat-label">活跃用户</view>
			</view>
		</view>

		<!-- 分类列表 -->
		<scroll-view class="category-list" scroll-y="true">
			<view 
				v-for="category in categories" 
				:key="category.id"
				class="category-card"
				@click="goToCategoryDetail(category.id)"
			>
				<view class="category-header">
					<view class="category-left">
						<view class="category-icon" :style="{backgroundColor: category.color}">
							{{category.icon}}
						</view>
						<view class="category-info">
							<view class="category-name">{{category.name}}</view>
							<view class="category-desc">{{category.description}}</view>
						</view>
					</view>
					<view class="category-stats">
						<view class="resource-count">{{category.resourceCount}}</view>
						<view class="count-label">个资源</view>
					</view>
				</view>
				
				<!-- 资源预览 -->
				<view class="resource-preview">
					<view 
						v-for="resource in category.previewResources" 
						:key="resource.id"
						class="preview-item"
						@click.stop="goToDetail(resource.id)"
					>
						<image 
							v-if="resource.previewImage" 
							class="preview-image" 
							:src="resource.previewImage" 
							mode="aspectFill"
						></image>
						<view v-else class="preview-placeholder" :style="{backgroundColor: category.color}">
							{{category.icon}}
						</view>
						<view class="preview-title">{{resource.title}}</view>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			stats: {
				totalResources: '1.2k',
				todayNew: '156',
				totalDownloads: '8.9k',
				activeUsers: '2.1k'
			},
			categories: [
				{
					id: 'ui',
					name: 'UI设计',
					description: '界面设计、组件库、设计规范',
					icon: '🎨',
					color: '#007aff',
					resourceCount: '342',
					previewResources: [
						{
							id: 1,
							title: '移动端UI Kit',
							previewImage: 'https://images.unsplash.com/photo-1561070791-2526d30994b5?w=120&h=80&fit=crop'
						},
						{
							id: 2,
							title: '线性图标集',
							previewImage: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=120&h=80&fit=crop'
						}
					]
				},
				{
					id: 'code',
					name: '开发工具',
					description: '代码模板、插件、脚本工具',
					icon: '💻',
					color: '#34c759',
					resourceCount: '189',
					previewResources: [
						{
							id: 3,
							title: 'Python脚本',
							previewImage: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=120&h=80&fit=crop'
						},
						{
							id: 4,
							title: 'VS Code插件',
							previewImage: 'https://images.unsplash.com/photo-1517180102446-f3ece451e9d8?w=120&h=80&fit=crop'
						}
					]
				},
				{
					id: 'image',
					name: '图片素材',
					description: '高清图片、插画、背景素材',
					icon: '🖼️',
					color: '#ff9500',
					resourceCount: '456',
					previewResources: [
						{
							id: 5,
							title: '抽象背景',
							previewImage: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=120&h=80&fit=crop'
						},
						{
							id: 6,
							title: '商务插画',
							previewImage: 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=120&h=80&fit=crop'
						}
					]
				},
				{
					id: 'audio',
					name: '音频素材',
					description: '背景音乐、音效、配音素材',
					icon: '🎵',
					color: '#af52de',
					resourceCount: '123',
					previewResources: [
						{
							id: 7,
							title: '轻音乐合集'
						},
						{
							id: 8,
							title: 'UI音效包'
						}
					]
				},
				{
					id: 'font',
					name: '字体文件',
					description: '中英文字体、艺术字体',
					icon: '🔤',
					color: '#ff3b30',
					resourceCount: '89',
					previewResources: [
						{
							id: 9,
							title: '思源黑体'
						},
						{
							id: 10,
							title: '书法字体'
						}
					]
				},
				{
					id: 'doc',
					name: '文档模板',
					description: '简历模板、PPT模板、合同模板',
					icon: '📄',
					color: '#5856d6',
					resourceCount: '67',
					previewResources: [
						{
							id: 11,
							title: '简历模板'
						},
						{
							id: 12,
							title: 'PPT模板'
						}
					]
				}
			]
		}
	},
	onLoad() {
		this.loadCategoryData();
	},
	methods: {
		loadCategoryData() {
			// TODO: 从API加载分类数据
			console.log('加载分类数据');
		},
		
		goToCategoryDetail(categoryId) {
			// 跳转到分类详情页（搜索页面，预设分类筛选）
			uni.navigateTo({
				url: `/pages/search/search?category=${categoryId}`
			});
		},
		
		goToDetail(resourceId) {
			uni.navigateTo({
				url: `/pages/detail/detail?id=${resourceId}`
			});
		}
	}
}
</script>

<style scoped>
.category-page {
	background-color: #f2f2f7;
	min-height: 100vh;
}

.header {
	background-color: #ffffff;
	padding: 48rpx 32rpx 32rpx;
}

.header-title {
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}

.header-desc {
	font-size: 28rpx;
	color: #666;
}

.stats-card {
	background-color: #ffffff;
	margin: 32rpx;
	border-radius: 24rpx;
	padding: 32rpx;
	display: flex;
	justify-content: space-between;
	box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.stat-item {
	text-align: center;
	flex: 1;
}

.stat-number {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}

.stat-label {
	font-size: 24rpx;
	color: #666;
}

.category-list {
	flex: 1;
	padding: 0 32rpx 32rpx;
}

.category-card {
	background-color: #ffffff;
	border-radius: 24rpx;
	padding: 32rpx;
	margin-bottom: 32rpx;
	box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.category-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 32rpx;
}

.category-left {
	display: flex;
	align-items: center;
	flex: 1;
}

.category-icon {
	width: 120rpx;
	height: 120rpx;
	border-radius: 32rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 48rpx;
	margin-right: 24rpx;
}

.category-info {
	flex: 1;
}

.category-name {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}

.category-desc {
	font-size: 24rpx;
	color: #666;
}

.category-stats {
	text-align: right;
}

.resource-count {
	font-size: 36rpx;
	font-weight: bold;
	color: #007aff;
	margin-bottom: 4rpx;
}

.count-label {
	font-size: 20rpx;
	color: #666;
}

.resource-preview {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 24rpx;
}

.preview-item {
	text-align: center;
}

.preview-image {
	width: 100%;
	height: 160rpx;
	border-radius: 16rpx;
	margin-bottom: 16rpx;
}

.preview-placeholder {
	width: 100%;
	height: 160rpx;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 48rpx;
	margin-bottom: 16rpx;
}

.preview-title {
	font-size: 24rpx;
	font-weight: 500;
	color: #333;
	line-height: 1.4;
}
</style>
