<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .status-bar {
            height: 44px;
            background: #fff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
        }
        .tab-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 83px;
            background: #fff;
            border-top: 1px solid #e5e5e5;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
        }
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #8e8e93;
            font-size: 10px;
        }
        .tab-item.active {
            color: #007aff;
        }
        .tab-item i {
            font-size: 24px;
            margin-bottom: 4px;
        }
        .content-area {
            height: calc(100vh - 127px);
            overflow-y: auto;
            background: #f2f2f7;
            padding-bottom: 20px;
        }
        .search-result-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .resource-icon {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- 状态栏 -->
    <div class="status-bar">
        <span>9:41</span>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
        <!-- 搜索头部 -->
        <div class="bg-white px-4 py-6">
            <h1 class="text-2xl font-bold text-gray-900 mb-4">搜索资源</h1>
            
            <!-- 搜索框 -->
            <div class="relative">
                <input type="text" placeholder="搜索" value="设计" 
                       class="w-full bg-gray-100 rounded-lg px-4 py-3 pl-10 pr-10 text-sm">
                <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                <i class="fas fa-microphone absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            </div>
            
            <!-- 搜索统计 -->
            <p class="text-gray-500 text-sm mt-3">为你找到 128 个"设计"相关的资源</p>
        </div>

        <!-- 筛选标签 -->
        <div class="px-4 py-3 bg-white border-t border-gray-100">
            <div class="flex space-x-2 overflow-x-auto">
                <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-xs whitespace-nowrap">全部</span>
                <span class="bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-xs whitespace-nowrap">UI设计</span>
                <span class="bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-xs whitespace-nowrap">图标</span>
                <span class="bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-xs whitespace-nowrap">模板</span>
                <span class="bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-xs whitespace-nowrap">字体</span>
            </div>
        </div>

        <!-- 搜索结果 -->
        <div class="px-4 mt-4">
            <!-- 搜索结果项 -->
            <div class="search-result-card">
                <div class="flex items-start space-x-3">
                    <div class="resource-icon bg-green-500">
                        <i class="fab fa-figma"></i>
                    </div>
                    <div class="flex-1">
                        <h3 class="font-semibold text-gray-900 text-sm mb-1">3D 图标合集</h3>
                        <p class="text-gray-500 text-xs mb-2">一套精美的3D风格图标，适用于各类设计项目</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-xs text-gray-400">
                                <i class="fas fa-user mr-1"></i>
                                <span class="mr-3">设计师小王</span>
                                <i class="fas fa-download mr-1"></i>
                                <span>2.5万</span>
                            </div>
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">免费</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="search-result-card">
                <div class="flex items-start space-x-3">
                    <div class="resource-icon bg-blue-500">
                        <i class="fas fa-layer-group"></i>
                    </div>
                    <div class="flex-1">
                        <h3 class="font-semibold text-gray-900 text-sm mb-1">线性图标包 (200+)</h3>
                        <p class="text-gray-500 text-xs mb-2">高质量的线性图标集合，包含SVG和PNG格式</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-xs text-gray-400">
                                <i class="fas fa-user mr-1"></i>
                                <span class="mr-3">Creative Studio</span>
                                <i class="fas fa-download mr-1"></i>
                                <span>1.8万</span>
                            </div>
                            <span class="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded">付费</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="search-result-card">
                <div class="flex items-start space-x-3">
                    <div class="resource-icon bg-purple-500">
                        <i class="fab fa-figma"></i>
                    </div>
                    <div class="flex-1">
                        <h3 class="font-semibold text-gray-900 text-sm mb-1">Figma 图标插件</h3>
                        <p class="text-gray-500 text-xs mb-2">一键插入各种设计图标的Figma插件</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-xs text-gray-400">
                                <i class="fas fa-user mr-1"></i>
                                <span class="mr-3">FigmaCN</span>
                                <i class="fas fa-download mr-1"></i>
                                <span>5.2万</span>
                            </div>
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">免费</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="search-result-card">
                <div class="flex items-start space-x-3">
                    <div class="resource-icon bg-red-500">
                        <i class="fas fa-paint-brush"></i>
                    </div>
                    <div class="flex-1">
                        <h3 class="font-semibold text-gray-900 text-sm mb-1">UI 设计规范模板</h3>
                        <p class="text-gray-500 text-xs mb-2">完整的移动端UI设计规范和组件库</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-xs text-gray-400">
                                <i class="fas fa-user mr-1"></i>
                                <span class="mr-3">UI大师</span>
                                <i class="fas fa-download mr-1"></i>
                                <span>3.1万</span>
                            </div>
                            <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">会员</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="search-result-card">
                <div class="flex items-start space-x-3">
                    <div class="resource-icon bg-yellow-500">
                        <i class="fas fa-font"></i>
                    </div>
                    <div class="flex-1">
                        <h3 class="font-semibold text-gray-900 text-sm mb-1">中文字体包合集</h3>
                        <p class="text-gray-500 text-xs mb-2">精选20款商用中文字体，支持商业使用</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-xs text-gray-400">
                                <i class="fas fa-user mr-1"></i>
                                <span class="mr-3">字体工坊</span>
                                <i class="fas fa-download mr-1"></i>
                                <span>4.5万</span>
                            </div>
                            <span class="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded">付费</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="tab-bar">
        <div class="tab-item">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="tab-item active">
            <i class="fas fa-search"></i>
            <span>搜索</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html>
