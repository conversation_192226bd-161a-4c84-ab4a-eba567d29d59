<template>
	<view class="upload-page">
		<scroll-view class="content" scroll-y="true">
			<!-- 基本信息 -->
			<view class="form-card">
				<view class="card-title">基本信息</view>
				
				<view class="form-group">
					<view class="form-label required">资源标题</view>
					<input 
						class="form-input" 
						type="text" 
						placeholder="请输入资源标题" 
						v-model="formData.title"
						maxlength="50"
					/>
				</view>

				<view class="form-group">
					<view class="form-label required">资源描述</view>
					<textarea 
						class="form-textarea" 
						placeholder="详细描述您的资源内容、特点和用途"
						v-model="formData.description"
						maxlength="500"
					></textarea>
				</view>

				<view class="form-group">
					<view class="form-label required">资源分类</view>
					<view class="category-grid">
						<view 
							v-for="category in categories" 
							:key="category.id"
							class="category-item" 
							:class="{selected: formData.categoryId === category.id}"
							@click="selectCategory(category.id)"
						>
							<text class="category-icon">{{category.icon}}</text>
							<text class="category-name">{{category.name}}</text>
						</view>
					</view>
				</view>

				<view class="form-group">
					<view class="form-label">标签</view>
					<input 
						class="form-input" 
						type="text" 
						placeholder="用逗号分隔多个标签，如：UI设计,移动端,Figma" 
						v-model="formData.tags"
					/>
				</view>
			</view>

			<!-- 资源链接 -->
			<view class="form-card">
				<view class="card-title">资源链接</view>
				
				<view class="form-group">
					<view class="form-label required">下载链接</view>
					<textarea 
						class="form-textarea link-input" 
						placeholder="请输入资源下载链接，支持网盘链接、GitHub链接等&#10;例如：https://pan.baidu.com/s/1234567890&#10;或：https://github.com/username/project"
						v-model="formData.downloadUrl"
					></textarea>
					<view class="form-tip">
						💡 支持百度网盘、阿里云盘、GitHub、蓝奏云等链接
					</view>
				</view>

				<view class="form-group">
					<view class="form-label">提取码</view>
					<input 
						class="form-input" 
						type="text" 
						placeholder="如果是网盘链接，请输入提取码" 
						v-model="formData.extractCode"
						maxlength="10"
					/>
				</view>
			</view>

			<!-- 详细信息 -->
			<view class="form-card">
				<view class="card-title">详细信息</view>
				
				<view class="form-group">
					<view class="form-label">兼容软件</view>
					<input 
						class="form-input" 
						type="text" 
						placeholder="如：Sketch 70+, Figma, Photoshop CC" 
						v-model="formData.compatibility"
					/>
				</view>

				<view class="form-group">
					<view class="form-label">文件格式</view>
					<input 
						class="form-input" 
						type="text" 
						placeholder="如：Sketch, Figma, PSD, PNG" 
						v-model="formData.fileFormat"
					/>
				</view>

				<view class="form-group">
					<view class="form-label">文件大小</view>
					<input 
						class="form-input" 
						type="text" 
						placeholder="如：45.2 MB" 
						v-model="formData.fileSize"
					/>
				</view>

				<view class="form-group">
					<view class="form-label">资源类型</view>
					<picker 
						:value="typeIndex" 
						:range="resourceTypes" 
						range-key="name"
						@change="onTypeChange"
					>
						<view class="picker-input">
							{{resourceTypes[typeIndex].name}}
							<text class="picker-arrow">></text>
						</view>
					</picker>
				</view>

				<view class="form-group" v-if="resourceTypes[typeIndex].id === 'paid'">
					<view class="form-label">价格 (元)</view>
					<input 
						class="form-input" 
						type="number" 
						placeholder="请输入价格" 
						v-model="formData.price"
					/>
				</view>
			</view>

			<!-- 预览图片 -->
			<view class="form-card">
				<view class="card-title">预览图片</view>
				<view class="upload-area" @click="chooseImage">
					<view v-if="!formData.previewImage" class="upload-placeholder">
						<text class="upload-icon">📷</text>
						<text class="upload-text">上传资源预览图</text>
						<text class="upload-tip">建议尺寸: 800x600px, 支持 JPG, PNG 格式</text>
					</view>
					<image v-else class="preview-image" :src="formData.previewImage" mode="aspectFill"></image>
				</view>
			</view>

			<!-- 使用协议 -->
			<view class="form-card">
				<view class="agreement-row">
					<checkbox 
						:checked="agreed" 
						@click="toggleAgreement"
						color="#007aff"
					/>
					<text class="agreement-text">
						我确认拥有此资源的版权或使用权，同意遵守平台的
						<text class="link-text">《资源分享协议》</text>
						和
						<text class="link-text">《版权声明》</text>
					</text>
				</view>
			</view>
		</scroll-view>

		<!-- 底部提交按钮 -->
		<view class="bottom-actions">
			<button class="submit-btn" :disabled="!canSubmit" @click="submitResource">
				<text class="btn-icon">🚀</text>
				<text class="btn-text">发布资源</text>
			</button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			agreed: false,
			typeIndex: 0,
			formData: {
				title: '',
				description: '',
				categoryId: '',
				tags: '',
				downloadUrl: '',
				extractCode: '',
				compatibility: '',
				fileFormat: '',
				fileSize: '',
				type: 'free',
				price: '',
				previewImage: ''
			},
			categories: [
				{id: 'ui', name: 'UI设计', icon: '🎨'},
				{id: 'code', name: '开发工具', icon: '💻'},
				{id: 'image', name: '图片素材', icon: '🖼️'},
				{id: 'audio', name: '音频素材', icon: '🎵'},
				{id: 'font', name: '字体文件', icon: '🔤'},
				{id: 'doc', name: '文档模板', icon: '📄'}
			],
			resourceTypes: [
				{id: 'free', name: '免费资源'},
				{id: 'paid', name: '付费资源'},
				{id: 'vip', name: '会员专享'}
			]
		}
	},
	computed: {
		canSubmit() {
			return this.formData.title && 
				   this.formData.description && 
				   this.formData.categoryId && 
				   this.formData.downloadUrl && 
				   this.agreed;
		}
	},
	methods: {
		selectCategory(categoryId) {
			this.formData.categoryId = categoryId;
		},
		
		onTypeChange(e) {
			this.typeIndex = e.detail.value;
			this.formData.type = this.resourceTypes[this.typeIndex].id;
		},
		
		toggleAgreement() {
			this.agreed = !this.agreed;
		},
		
		chooseImage() {
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					this.formData.previewImage = res.tempFilePaths[0];
					// TODO: 上传图片到服务器
					this.uploadImage(res.tempFilePaths[0]);
				}
			});
		},
		
		uploadImage(imagePath) {
			uni.showLoading({
				title: '上传中...'
			});
			
			// TODO: 实际上传逻辑
			setTimeout(() => {
				uni.hideLoading();
				uni.showToast({
					title: '上传成功',
					icon: 'success'
				});
			}, 1000);
		},
		
		submitResource() {
			if (!this.canSubmit) {
				uni.showToast({
					title: '请完善必填信息',
					icon: 'none'
				});
				return;
			}
			
			uni.showLoading({
				title: '发布中...'
			});
			
			// 构建提交数据
			const submitData = {
				...this.formData,
				tags: this.formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag)
			};
			
			// TODO: 调用API提交数据
			console.log('提交数据:', submitData);
			
			setTimeout(() => {
				uni.hideLoading();
				uni.showToast({
					title: '发布成功',
					icon: 'success'
				});
				
				// 返回上一页
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			}, 2000);
		}
	}
}
</script>

<style scoped>
.upload-page {
	background-color: #f2f2f7;
	min-height: 100vh;
	padding-bottom: 160rpx;
}

.content {
	height: calc(100vh - 160rpx);
}

.form-card {
	background-color: #ffffff;
	margin: 32rpx;
	border-radius: 24rpx;
	padding: 32rpx;
	box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.card-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 32rpx;
}

.form-group {
	margin-bottom: 32rpx;
}

.form-label {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 16rpx;
}

.form-label.required::after {
	content: ' *';
	color: #ff3b30;
}

.form-input {
	width: 100%;
	height: 80rpx;
	padding: 0 24rpx;
	border: 2rpx solid #e5e5e5;
	border-radius: 16rpx;
	font-size: 28rpx;
	background-color: #ffffff;
}

.form-input:focus {
	border-color: #007aff;
}

.form-textarea {
	width: 100%;
	min-height: 160rpx;
	padding: 24rpx;
	border: 2rpx solid #e5e5e5;
	border-radius: 16rpx;
	font-size: 28rpx;
	background-color: #ffffff;
	line-height: 1.5;
}

.form-textarea.link-input {
	min-height: 120rpx;
}

.form-textarea:focus {
	border-color: #007aff;
}

.form-tip {
	font-size: 24rpx;
	color: #666;
	margin-top: 8rpx;
}

.category-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 24rpx;
}

.category-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 24rpx;
	border: 2rpx solid #e5e5e5;
	border-radius: 16rpx;
	background-color: #ffffff;
}

.category-item.selected {
	border-color: #007aff;
	background-color: #f0f8ff;
}

.category-icon {
	font-size: 40rpx;
	margin-bottom: 8rpx;
}

.category-name {
	font-size: 24rpx;
	color: #333;
}

.picker-input {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 80rpx;
	padding: 0 24rpx;
	border: 2rpx solid #e5e5e5;
	border-radius: 16rpx;
	font-size: 28rpx;
	background-color: #ffffff;
}

.picker-arrow {
	color: #ccc;
	font-size: 32rpx;
}

.upload-area {
	border: 2rpx dashed #d1d5db;
	border-radius: 16rpx;
	padding: 80rpx 40rpx;
	text-align: center;
	background-color: #f9fafb;
}

.upload-placeholder {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.upload-icon {
	font-size: 80rpx;
	margin-bottom: 16rpx;
}

.upload-text {
	font-size: 32rpx;
	color: #666;
	margin-bottom: 8rpx;
}

.upload-tip {
	font-size: 24rpx;
	color: #999;
}

.preview-image {
	width: 100%;
	height: 300rpx;
	border-radius: 12rpx;
}

.agreement-row {
	display: flex;
	align-items: flex-start;
}

.agreement-text {
	flex: 1;
	font-size: 24rpx;
	color: #666;
	line-height: 1.6;
	margin-left: 16rpx;
}

.link-text {
	color: #007aff;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #ffffff;
	padding: 32rpx;
	border-top: 1rpx solid #f0f0f0;
}

.submit-btn {
	width: 100%;
	height: 88rpx;
	background-color: #007aff;
	color: #ffffff;
	border: none;
	border-radius: 24rpx;
	font-size: 32rpx;
	font-weight: 600;
	display: flex;
	align-items: center;
	justify-content: center;
}

.submit-btn:disabled {
	background-color: #ccc;
}

.submit-btn::after {
	border: none;
}

.btn-icon {
	margin-right: 8rpx;
	font-size: 28rpx;
}
</style>
