<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分类浏览</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .status-bar {
            height: 44px;
            background: #fff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
        }
        .tab-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 83px;
            background: #fff;
            border-top: 1px solid #e5e5e5;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
        }
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #8e8e93;
            font-size: 10px;
        }
        .tab-item.active {
            color: #007aff;
        }
        .tab-item i {
            font-size: 24px;
            margin-bottom: 4px;
        }
        .content-area {
            height: calc(100vh - 127px);
            overflow-y: auto;
            background: #f2f2f7;
            padding-bottom: 20px;
        }
        .category-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .category-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin-bottom: 12px;
        }
        .resource-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-top: 16px;
        }
        .resource-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            text-align: center;
        }
        .resource-thumb {
            width: 100%;
            height: 80px;
            border-radius: 6px;
            object-fit: cover;
            margin-bottom: 8px;
        }
        .stats-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: white;
            margin: 16px;
            padding: 16px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .stat-item {
            text-align: center;
            flex: 1;
        }
        .stat-number {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- 状态栏 -->
    <div class="status-bar">
        <span>9:41</span>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
        <!-- 头部 -->
        <div class="bg-white px-4 py-6">
            <h1 class="text-2xl font-bold text-gray-900 mb-2">分类浏览</h1>
            <p class="text-gray-500 text-sm">探索不同类型的优质资源</p>
        </div>

        <!-- 统计信息 -->
        <div class="stats-bar">
            <div class="stat-item">
                <div class="stat-number">1.2k</div>
                <div class="stat-label">总资源</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">156</div>
                <div class="stat-label">今日新增</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">8.9k</div>
                <div class="stat-label">总下载</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">2.1k</div>
                <div class="stat-label">活跃用户</div>
            </div>
        </div>

        <!-- 分类列表 -->
        <div class="px-4">
            <!-- UI设计分类 -->
            <div class="category-card">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center space-x-3">
                        <div class="category-icon bg-blue-500">
                            <i class="fas fa-palette"></i>
                        </div>
                        <div>
                            <h2 class="text-lg font-semibold text-gray-900">UI设计</h2>
                            <p class="text-sm text-gray-500">界面设计、组件库、设计规范</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-lg font-bold text-blue-500">342</p>
                        <p class="text-xs text-gray-500">个资源</p>
                    </div>
                </div>
                
                <div class="resource-grid">
                    <div class="resource-item">
                        <img src="https://images.unsplash.com/photo-1561070791-2526d30994b5?w=120&h=80&fit=crop" 
                             alt="UI Kit" class="resource-thumb">
                        <p class="text-xs font-medium text-gray-900">移动端UI Kit</p>
                    </div>
                    <div class="resource-item">
                        <img src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=120&h=80&fit=crop" 
                             alt="图标" class="resource-thumb">
                        <p class="text-xs font-medium text-gray-900">线性图标集</p>
                    </div>
                </div>
            </div>

            <!-- 开发工具分类 -->
            <div class="category-card">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center space-x-3">
                        <div class="category-icon bg-green-500">
                            <i class="fas fa-code"></i>
                        </div>
                        <div>
                            <h2 class="text-lg font-semibold text-gray-900">开发工具</h2>
                            <p class="text-sm text-gray-500">代码模板、插件、脚本工具</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-lg font-bold text-green-500">189</p>
                        <p class="text-xs text-gray-500">个资源</p>
                    </div>
                </div>
                
                <div class="resource-grid">
                    <div class="resource-item">
                        <img src="https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=120&h=80&fit=crop" 
                             alt="Python" class="resource-thumb">
                        <p class="text-xs font-medium text-gray-900">Python脚本</p>
                    </div>
                    <div class="resource-item">
                        <img src="https://images.unsplash.com/photo-1517180102446-f3ece451e9d8?w=120&h=80&fit=crop" 
                             alt="插件" class="resource-thumb">
                        <p class="text-xs font-medium text-gray-900">VS Code插件</p>
                    </div>
                </div>
            </div>

            <!-- 图片素材分类 -->
            <div class="category-card">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center space-x-3">
                        <div class="category-icon bg-orange-500">
                            <i class="fas fa-image"></i>
                        </div>
                        <div>
                            <h2 class="text-lg font-semibold text-gray-900">图片素材</h2>
                            <p class="text-sm text-gray-500">高清图片、插画、背景素材</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-lg font-bold text-orange-500">456</p>
                        <p class="text-xs text-gray-500">个资源</p>
                    </div>
                </div>
                
                <div class="resource-grid">
                    <div class="resource-item">
                        <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=120&h=80&fit=crop" 
                             alt="背景" class="resource-thumb">
                        <p class="text-xs font-medium text-gray-900">抽象背景</p>
                    </div>
                    <div class="resource-item">
                        <img src="https://images.unsplash.com/photo-1557804506-669a67965ba0?w=120&h=80&fit=crop" 
                             alt="插画" class="resource-thumb">
                        <p class="text-xs font-medium text-gray-900">商务插画</p>
                    </div>
                </div>
            </div>

            <!-- 音频素材分类 -->
            <div class="category-card">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center space-x-3">
                        <div class="category-icon bg-purple-500">
                            <i class="fas fa-music"></i>
                        </div>
                        <div>
                            <h2 class="text-lg font-semibold text-gray-900">音频素材</h2>
                            <p class="text-sm text-gray-500">背景音乐、音效、配音素材</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-lg font-bold text-purple-500">123</p>
                        <p class="text-xs text-gray-500">个资源</p>
                    </div>
                </div>
                
                <div class="resource-grid">
                    <div class="resource-item">
                        <div class="w-full h-20 bg-gradient-to-r from-purple-400 to-pink-400 rounded-md flex items-center justify-center mb-2">
                            <i class="fas fa-music text-white text-2xl"></i>
                        </div>
                        <p class="text-xs font-medium text-gray-900">轻音乐合集</p>
                    </div>
                    <div class="resource-item">
                        <div class="w-full h-20 bg-gradient-to-r from-blue-400 to-purple-400 rounded-md flex items-center justify-center mb-2">
                            <i class="fas fa-volume-up text-white text-2xl"></i>
                        </div>
                        <p class="text-xs font-medium text-gray-900">UI音效包</p>
                    </div>
                </div>
            </div>

            <!-- 字体文件分类 -->
            <div class="category-card">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center space-x-3">
                        <div class="category-icon bg-red-500">
                            <i class="fas fa-font"></i>
                        </div>
                        <div>
                            <h2 class="text-lg font-semibold text-gray-900">字体文件</h2>
                            <p class="text-sm text-gray-500">中英文字体、艺术字体</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-lg font-bold text-red-500">89</p>
                        <p class="text-xs text-gray-500">个资源</p>
                    </div>
                </div>
                
                <div class="resource-grid">
                    <div class="resource-item">
                        <div class="w-full h-20 bg-gradient-to-r from-red-400 to-pink-400 rounded-md flex items-center justify-center mb-2">
                            <span class="text-white text-lg font-bold">Aa</span>
                        </div>
                        <p class="text-xs font-medium text-gray-900">思源黑体</p>
                    </div>
                    <div class="resource-item">
                        <div class="w-full h-20 bg-gradient-to-r from-yellow-400 to-red-400 rounded-md flex items-center justify-center mb-2">
                            <span class="text-white text-lg font-bold">字</span>
                        </div>
                        <p class="text-xs font-medium text-gray-900">书法字体</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="tab-bar">
        <div class="tab-item">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-search"></i>
            <span>搜索</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html>
