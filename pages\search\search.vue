<template>
	<view class="search-page">
		<!-- 自定义状态栏 -->
		<view class="status-bar" :style="{height: statusBarHeight + 'px'}"></view>
		
		<!-- 搜索头部 -->
		<view class="search-header">
			<view class="search-title">搜索资源</view>
			<view class="search-box">
				<input 
					class="search-input" 
					type="text" 
					placeholder="搜索资源、作者、标签..." 
					v-model="searchKeyword"
					@input="onSearchInput"
					@confirm="onSearch"
				/>
				<text class="search-icon">🔍</text>
			</view>
			<view class="search-stats" v-if="searchResults.length > 0">
				为你找到 {{searchResults.length}} 个相关资源
			</view>
		</view>

		<!-- 筛选标签 -->
		<view class="filter-tags">
			<scroll-view class="tags-scroll" scroll-x="true">
				<view class="tag-item" 
					:class="{active: currentCategory === ''}" 
					@click="filterByCategory('')">
					全部
				</view>
				<view class="tag-item" 
					v-for="category in categories" 
					:key="category.id"
					:class="{active: currentCategory === category.id}" 
					@click="filterByCategory(category.id)">
					{{category.name}}
				</view>
			</scroll-view>
		</view>

		<!-- 搜索结果 -->
		<scroll-view class="search-results" scroll-y="true" @scrolltolower="loadMore">
			<view class="result-item" 
				v-for="item in displayResults" 
				:key="item.id"
				@click="goToDetail(item.id)">
				<view class="result-icon" :style="{backgroundColor: item.categoryColor}">
					{{item.categoryIcon}}
				</view>
				<view class="result-content">
					<view class="result-title">{{item.title}}</view>
					<view class="result-desc">{{item.description}}</view>
					<view class="result-meta">
						<text class="meta-item">👤 {{item.author}}</text>
						<text class="meta-item">📥 {{item.downloads}}</text>
						<text class="meta-item">⭐ {{item.rating}}</text>
					</view>
				</view>
				<view class="result-tag" :class="item.type">{{item.typeText}}</view>
			</view>
			
			<!-- 加载更多 -->
			<view class="load-more" v-if="hasMore">
				<text>加载更多...</text>
			</view>
			
			<!-- 暂无数据 -->
			<view class="no-data" v-if="searchResults.length === 0 && searchKeyword">
				<text>😔 暂无相关资源</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			statusBarHeight: 0,
			searchKeyword: '',
			currentCategory: '',
			searchResults: [],
			displayResults: [],
			hasMore: true,
			page: 1,
			pageSize: 10,
			categories: [
				{id: 'ui', name: 'UI设计', icon: '🎨'},
				{id: 'code', name: '开发工具', icon: '💻'},
				{id: 'image', name: '图片素材', icon: '🖼️'},
				{id: 'audio', name: '音频素材', icon: '🎵'},
				{id: 'font', name: '字体文件', icon: '🔤'},
				{id: 'doc', name: '文档模板', icon: '📄'}
			],
			// 模拟数据
			allResources: [
				{
					id: 1,
					title: '3D 图标合集',
					description: '一套精美的3D风格图标，适用于各类设计项目',
					author: '设计师小王',
					downloads: '2.5万',
					rating: '4.8',
					category: 'ui',
					categoryIcon: '🎨',
					categoryColor: '#007aff',
					type: 'free',
					typeText: '免费',
					downloadUrl: 'https://example.com/3d-icons.zip'
				},
				{
					id: 2,
					title: 'Python 自动化脚本',
					description: '一套完整的Python爬虫，提高工作效率',
					author: '开发者老李',
					downloads: '1.8万',
					rating: '4.9',
					category: 'code',
					categoryIcon: '💻',
					categoryColor: '#34c759',
					type: 'paid',
					typeText: '付费',
					downloadUrl: 'https://github.com/example/python-scripts'
				}
			]
		}
	},
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight;
		
		// 初始化数据
		this.loadResources();
	},
	methods: {
		onSearchInput() {
			// 实时搜索
			this.debounceSearch();
		},
		
		onSearch() {
			this.performSearch();
		},
		
		debounceSearch() {
			clearTimeout(this.searchTimer);
			this.searchTimer = setTimeout(() => {
				this.performSearch();
			}, 500);
		},
		
		performSearch() {
			if (!this.searchKeyword.trim()) {
				this.searchResults = [...this.allResources];
			} else {
				this.searchResults = this.allResources.filter(item => 
					item.title.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
					item.description.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
					item.author.toLowerCase().includes(this.searchKeyword.toLowerCase())
				);
			}
			this.filterResults();
		},
		
		filterByCategory(categoryId) {
			this.currentCategory = categoryId;
			this.filterResults();
		},
		
		filterResults() {
			let results = [...this.searchResults];
			
			if (this.currentCategory) {
				results = results.filter(item => item.category === this.currentCategory);
			}
			
			this.displayResults = results.slice(0, this.page * this.pageSize);
			this.hasMore = results.length > this.displayResults.length;
		},
		
		loadMore() {
			if (this.hasMore) {
				this.page++;
				this.filterResults();
			}
		},
		
		loadResources() {
			// 模拟加载数据
			this.searchResults = [...this.allResources];
			this.displayResults = this.searchResults.slice(0, this.pageSize);
		},
		
		goToDetail(id) {
			uni.navigateTo({
				url: `/pages/detail/detail?id=${id}`
			});
		}
	}
}
</script>

<style scoped>
.search-page {
	background-color: #f2f2f7;
	min-height: 100vh;
}

.search-header {
	background-color: #ffffff;
	padding: 20rpx 32rpx 32rpx;
}

.search-title {
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 32rpx;
}

.search-box {
	position: relative;
	margin-bottom: 24rpx;
}

.search-input {
	width: 100%;
	height: 80rpx;
	background-color: #f5f5f5;
	border-radius: 16rpx;
	padding: 0 32rpx 0 80rpx;
	font-size: 28rpx;
}

.search-icon {
	position: absolute;
	left: 24rpx;
	top: 50%;
	transform: translateY(-50%);
	font-size: 32rpx;
}

.search-stats {
	font-size: 24rpx;
	color: #666;
}

.filter-tags {
	background-color: #ffffff;
	border-top: 1rpx solid #f0f0f0;
	padding: 24rpx 0;
}

.tags-scroll {
	white-space: nowrap;
	padding: 0 32rpx;
}

.tag-item {
	display: inline-block;
	padding: 16rpx 32rpx;
	margin-right: 16rpx;
	background-color: #f5f5f5;
	color: #666;
	border-radius: 32rpx;
	font-size: 24rpx;
	white-space: nowrap;
}

.tag-item.active {
	background-color: #007aff;
	color: #ffffff;
}

.search-results {
	flex: 1;
	padding: 32rpx;
}

.result-item {
	background-color: #ffffff;
	border-radius: 24rpx;
	padding: 32rpx;
	margin-bottom: 24rpx;
	display: flex;
	align-items: flex-start;
	box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.result-icon {
	width: 96rpx;
	height: 96rpx;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 40rpx;
	margin-right: 24rpx;
}

.result-content {
	flex: 1;
}

.result-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}

.result-desc {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 16rpx;
	line-height: 1.4;
}

.result-meta {
	display: flex;
	align-items: center;
}

.meta-item {
	font-size: 20rpx;
	color: #999;
	margin-right: 24rpx;
}

.result-tag {
	padding: 8rpx 16rpx;
	border-radius: 8rpx;
	font-size: 20rpx;
	align-self: flex-start;
}

.result-tag.free {
	background-color: #e8f5e8;
	color: #34c759;
}

.result-tag.paid {
	background-color: #fff3e0;
	color: #ff9500;
}

.result-tag.vip {
	background-color: #f0f0ff;
	color: #5856d6;
}

.load-more, .no-data {
	text-align: center;
	padding: 40rpx;
	color: #999;
	font-size: 28rpx;
}
</style>
