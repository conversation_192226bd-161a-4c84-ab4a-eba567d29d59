<template>
	<view class="home-page">
		<!-- 自定义状态栏 -->
		<view class="status-bar" :style="{height: statusBarHeight + 'px'}"></view>

		<!-- 头部 -->
		<view class="header">
			<view class="header-content">
				<view class="title-section">
					<view class="app-title">资源中心</view>
					<view class="app-subtitle">发现、分享、创造</view>
				</view>
				<view class="user-avatar" @click="goToProfile">
					<image class="avatar-img" :src="userInfo.avatar" mode="aspectFill"></image>
				</view>
			</view>

			<!-- 搜索框 -->
			<view class="search-box" @click="goToSearch">
				<text class="search-icon">🔍</text>
				<text class="search-placeholder">搜索设计资源、代码模板...</text>
			</view>
		</view>

		<!-- 分类浏览 -->
		<view class="category-section">
			<view class="section-title">分类浏览</view>
			<view class="category-grid">
				<view
					v-for="category in categories"
					:key="category.id"
					class="category-item"
					@click="goToCategory(category.id)"
				>
					<view class="category-icon" :style="{backgroundColor: category.color}">
						{{category.icon}}
					</view>
					<text class="category-name">{{category.name}}</text>
				</view>
			</view>
		</view>

		<!-- 精选推荐 -->
		<view class="recommend-section">
			<view class="section-header">
				<view class="section-title">精选推荐</view>
				<text class="more-link" @click="goToSearch">查看全部</text>
			</view>

			<scroll-view class="recommend-list" scroll-y="true" @scrolltolower="loadMoreRecommend">
				<view
					v-for="item in recommendList"
					:key="item.id"
					class="resource-card"
					@click="goToDetail(item.id)"
				>
					<view class="card-content">
						<image class="resource-image" :src="item.previewImage" mode="aspectFill"></image>
						<view class="resource-info">
							<view class="resource-header">
								<view class="category-tag" :style="{backgroundColor: item.categoryColor}">
									{{item.categoryName}}
								</view>
							</view>
							<view class="resource-title">{{item.title}}</view>
							<view class="resource-desc">{{item.description}}</view>
							<view class="resource-meta">
								<text class="meta-item">👤 {{item.author}}</text>
								<text class="meta-item">📥 {{item.downloads}}</text>
								<text class="meta-item">⭐ {{item.rating}}</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 加载更多 -->
				<view class="load-more" v-if="hasMore">
					<text>加载更多...</text>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			statusBarHeight: 0,
			hasMore: true,
			userInfo: {
				nickname: '管理员',
				avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&crop=face'
			},
			categories: [
				{id: 'ui', name: 'UI设计', icon: '🎨', color: '#007aff'},
				{id: 'code', name: '开发', icon: '💻', color: '#34c759'},
				{id: 'image', name: '素材', icon: '🖼️', color: '#ff9500'},
				{id: 'audio', name: '音频', icon: '🎵', color: '#af52de'},
				{id: 'font', name: '字体', icon: '🔤', color: '#ff3b30'},
				{id: 'doc', name: '文档', icon: '📄', color: '#5856d6'}
			],
			recommendList: [
				{
					id: 1,
					title: 'Python 自动化脚本',
					description: '一套完整的Python爬虫，提高工作效率',
					author: '设计师小王',
					downloads: '2.5万',
					rating: '4.8',
					categoryName: '开发',
					categoryColor: '#34c759',
					previewImage: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=120&h=120&fit=crop'
				},
				{
					id: 2,
					title: '红文娱乐 UI Kit',
					description: '完整的娱乐类应用UI组件设计',
					author: 'Creative Studio',
					downloads: '1.8万',
					rating: '4.9',
					categoryName: 'UI设计',
					categoryColor: '#007aff',
					previewImage: 'https://images.unsplash.com/photo-1561070791-2526d30994b5?w=120&h=120&fit=crop'
				},
				{
					id: 3,
					title: '无版权背景音乐包',
					description: '50首高质量无版权背景音乐',
					author: '音乐工作室',
					downloads: '3.2万',
					rating: '4.7',
					categoryName: '音频',
					categoryColor: '#af52de',
					previewImage: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=120&h=120&fit=crop'
				}
			]
		}
	},
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight;

		// 检查登录状态
		this.checkLoginStatus();

		// 加载数据
		this.loadUserInfo();
		this.loadRecommendList();
	},
	onShow() {
		// 页面显示时刷新数据
		this.loadUserInfo();
	},
	methods: {
		checkLoginStatus() {
			const token = uni.getStorageSync('token');
			if (!token) {
				// 未登录，跳转到登录页
				uni.reLaunch({
					url: '/pages/login/login'
				});
			}
		},

		loadUserInfo() {
			const userInfo = uni.getStorageSync('userInfo');
			if (userInfo) {
				this.userInfo = userInfo;
			}
		},

		loadRecommendList() {
			// TODO: 从API加载推荐资源
			console.log('加载推荐资源');
		},

		loadMoreRecommend() {
			if (this.hasMore) {
				// TODO: 加载更多推荐资源
				console.log('加载更多推荐资源');
			}
		},

		goToSearch() {
			uni.switchTab({
				url: '/pages/search/search'
			});
		},

		goToProfile() {
			uni.switchTab({
				url: '/pages/profile/profile'
			});
		},

		goToCategory(categoryId) {
			uni.navigateTo({
				url: `/pages/category/category?category=${categoryId}`
			});
		},

		goToDetail(resourceId) {
			uni.navigateTo({
				url: `/pages/detail/detail?id=${resourceId}`
			});
		}
	}
}
</script>

<style scoped>
.home-page {
	background-color: #f2f2f7;
	min-height: 100vh;
}

.header {
	background-color: #ffffff;
	padding: 32rpx;
}

.header-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 32rpx;
}

.title-section {
	flex: 1;
}

.app-title {
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}

.app-subtitle {
	font-size: 28rpx;
	color: #666;
}

.user-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	overflow: hidden;
}

.avatar-img {
	width: 100%;
	height: 100%;
}

.search-box {
	display: flex;
	align-items: center;
	background-color: #f5f5f5;
	border-radius: 16rpx;
	padding: 24rpx 32rpx;
}

.search-icon {
	font-size: 32rpx;
	margin-right: 16rpx;
	color: #999;
}

.search-placeholder {
	font-size: 28rpx;
	color: #999;
}

.category-section {
	padding: 48rpx 32rpx;
}

.section-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 32rpx;
}

.section-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 32rpx;
}

.more-link {
	font-size: 28rpx;
	color: #007aff;
}

.category-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 32rpx;
}

.category-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
}

.category-icon {
	width: 120rpx;
	height: 120rpx;
	border-radius: 32rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 48rpx;
	margin-bottom: 16rpx;
}

.category-name {
	font-size: 24rpx;
	color: #666;
}

.recommend-section {
	padding: 0 32rpx 32rpx;
}

.recommend-list {
	max-height: 800rpx;
}

.resource-card {
	background-color: #ffffff;
	border-radius: 24rpx;
	margin-bottom: 24rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.card-content {
	display: flex;
	padding: 32rpx;
}

.resource-image {
	width: 120rpx;
	height: 120rpx;
	border-radius: 16rpx;
	margin-right: 24rpx;
}

.resource-info {
	flex: 1;
}

.resource-header {
	margin-bottom: 8rpx;
}

.category-tag {
	display: inline-block;
	padding: 4rpx 12rpx;
	border-radius: 8rpx;
	color: #ffffff;
	font-size: 20rpx;
}

.resource-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}

.resource-desc {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 16rpx;
	line-height: 1.4;
}

.resource-meta {
	display: flex;
	align-items: center;
}

.meta-item {
	font-size: 20rpx;
	color: #999;
	margin-right: 24rpx;
}

.load-more {
	text-align: center;
	padding: 40rpx;
	color: #999;
	font-size: 28rpx;
}
</style>