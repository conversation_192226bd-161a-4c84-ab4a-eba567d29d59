<template>
	<view class="detail-page">
		<!-- 资源预览图 -->
		<view class="preview-section">
			<image class="preview-image" :src="resourceInfo.previewImage" mode="aspectFill"></image>
		</view>

		<!-- 资源信息 -->
		<view class="info-card">
			<view class="title-row">
				<view class="resource-title">{{resourceInfo.title}}</view>
				<view class="category-tag" :style="{backgroundColor: resourceInfo.categoryColor}">
					{{resourceInfo.categoryName}}
				</view>
			</view>
			
			<view class="stats-row">
				<text class="stat-item">📥 {{resourceInfo.downloads}} 下载</text>
				<text class="stat-item">👁️ {{resourceInfo.views}} 浏览</text>
				<text class="stat-item">❤️ {{resourceInfo.likes}} 喜欢</text>
			</view>

			<!-- 评分 -->
			<view class="rating-row">
				<view class="stars">
					<text v-for="i in 5" :key="i" class="star" :class="{filled: i <= resourceInfo.rating}">⭐</text>
				</view>
				<text class="rating-text">{{resourceInfo.rating}}.0 ({{resourceInfo.reviewCount}} 评价)</text>
			</view>

			<!-- 作者信息 -->
			<view class="author-row">
				<image class="author-avatar" :src="resourceInfo.author.avatar" mode="aspectFill"></image>
				<view class="author-info">
					<view class="author-name">{{resourceInfo.author.name}}</view>
					<view class="author-desc">{{resourceInfo.author.description}}</view>
				</view>
				<button class="follow-btn" :class="{followed: isFollowed}" @click="toggleFollow">
					{{isFollowed ? '已关注' : '关注'}}
				</button>
			</view>
		</view>

		<!-- 资源描述 -->
		<view class="desc-card">
			<view class="card-title">资源描述</view>
			<view class="desc-content">{{resourceInfo.description}}</view>
			
			<view class="features-title">包含内容：</view>
			<view class="features-list">
				<text v-for="feature in resourceInfo.features" :key="feature" class="feature-item">
					• {{feature}}
				</text>
			</view>

			<!-- 标签 -->
			<view class="tags-section">
				<view class="tags-title">标签：</view>
				<view class="tags-list">
					<text v-for="tag in resourceInfo.tags" :key="tag" class="tag">{{tag}}</text>
				</view>
			</view>
		</view>

		<!-- 文件信息 -->
		<view class="file-card">
			<view class="card-title">文件信息</view>
			<view class="file-info">
				<view class="info-row">
					<text class="info-label">文件大小</text>
					<text class="info-value">{{resourceInfo.fileSize}}</text>
				</view>
				<view class="info-row">
					<text class="info-label">文件格式</text>
					<text class="info-value">{{resourceInfo.fileFormat}}</text>
				</view>
				<view class="info-row">
					<text class="info-label">更新时间</text>
					<text class="info-value">{{resourceInfo.updateTime}}</text>
				</view>
				<view class="info-row">
					<text class="info-label">兼容版本</text>
					<text class="info-value">{{resourceInfo.compatibility}}</text>
				</view>
				<view class="info-row">
					<text class="info-label">下载链接</text>
					<text class="info-value link-text" @click="copyDownloadLink">
						{{resourceInfo.downloadUrl}}
					</text>
				</view>
			</view>
		</view>

		<!-- 相关推荐 -->
		<view class="related-card">
			<view class="card-title">相关推荐</view>
			<view class="related-list">
				<view v-for="item in relatedResources" :key="item.id" class="related-item" @click="goToDetail(item.id)">
					<image class="related-image" :src="item.previewImage" mode="aspectFill"></image>
					<view class="related-info">
						<view class="related-title">{{item.title}}</view>
						<view class="related-desc">{{item.description}}</view>
					</view>
					<text class="related-arrow">></text>
				</view>
			</view>
		</view>

		<!-- 底部操作栏 -->
		<view class="bottom-actions">
			<button class="action-btn secondary" @click="toggleLike">
				<text class="btn-icon">{{isLiked ? '❤️' : '🤍'}}</text>
				<text class="btn-text">{{isLiked ? '已收藏' : '收藏'}}</text>
			</button>
			<button class="action-btn primary" @click="copyDownloadLink">
				<text class="btn-icon">📋</text>
				<text class="btn-text">复制链接</text>
			</button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			resourceId: '',
			isFollowed: false,
			isLiked: false,
			resourceInfo: {
				title: '红文娱乐 UI Kit',
				categoryName: 'UI设计',
				categoryColor: '#007aff',
				downloads: '1.8万',
				views: '5.2万',
				likes: '3.1k',
				rating: 5,
				reviewCount: 326,
				previewImage: 'https://images.unsplash.com/photo-1561070791-2526d30994b5?w=400&h=200&fit=crop',
				author: {
					name: 'Creative Studio',
					description: '专业UI设计团队',
					avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&crop=face'
				},
				description: '这是一套完整的娱乐类应用UI组件设计，包含了登录注册、首页、个人中心、设置等常用页面的设计稿。设计风格现代简洁，色彩搭配和谐，适合各种娱乐类移动应用。',
				features: [
					'20+ 精美页面设计',
					'100+ UI组件',
					'Sketch & Figma 源文件',
					'设计规范文档',
					'图标字体文件'
				],
				tags: ['UI设计', '移动端', '娱乐', 'Sketch', 'Figma'],
				fileSize: '45.2 MB',
				fileFormat: 'Sketch, Figma, PNG',
				updateTime: '2024-01-15',
				compatibility: 'Sketch 70+, Figma',
				downloadUrl: 'https://pan.baidu.com/s/1234567890abcdef'
			},
			relatedResources: [
				{
					id: 2,
					title: '电商APP UI Kit',
					description: '完整的电商应用界面设计',
					previewImage: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=60&h=60&fit=crop'
				},
				{
					id: 3,
					title: '社交APP界面',
					description: '现代化社交应用设计模板',
					previewImage: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=60&h=60&fit=crop'
				}
			]
		}
	},
	onLoad(options) {
		if (options.id) {
			this.resourceId = options.id;
			this.loadResourceDetail();
		}
	},
	methods: {
		loadResourceDetail() {
			// TODO: 根据resourceId加载资源详情
			console.log('加载资源详情:', this.resourceId);
		},
		
		toggleFollow() {
			this.isFollowed = !this.isFollowed;
			uni.showToast({
				title: this.isFollowed ? '关注成功' : '取消关注',
				icon: 'success'
			});
		},
		
		toggleLike() {
			this.isLiked = !this.isLiked;
			uni.showToast({
				title: this.isLiked ? '收藏成功' : '取消收藏',
				icon: 'success'
			});
		},
		
		copyDownloadLink() {
			// 复制下载链接到剪贴板
			uni.setClipboardData({
				data: this.resourceInfo.downloadUrl,
				success: () => {
					uni.showToast({
						title: '链接已复制',
						icon: 'success'
					});
					
					// 增加下载次数
					this.incrementDownloadCount();
				},
				fail: () => {
					uni.showToast({
						title: '复制失败',
						icon: 'error'
					});
				}
			});
		},
		
		incrementDownloadCount() {
			// TODO: 调用API增加下载次数
			console.log('增加下载次数');
		},
		
		goToDetail(id) {
			uni.redirectTo({
				url: `/pages/detail/detail?id=${id}`
			});
		}
	}
}
</script>

<style scoped>
.detail-page {
	background-color: #f2f2f7;
	padding-bottom: 160rpx;
}

.preview-section {
	padding: 32rpx;
}

.preview-image {
	width: 100%;
	height: 400rpx;
	border-radius: 24rpx;
}

.info-card, .desc-card, .file-card, .related-card {
	background-color: #ffffff;
	margin: 0 32rpx 32rpx;
	border-radius: 24rpx;
	padding: 32rpx;
	box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.title-row {
	display: flex;
	align-items: flex-start;
	justify-content: space-between;
	margin-bottom: 24rpx;
}

.resource-title {
	flex: 1;
	font-size: 40rpx;
	font-weight: bold;
	color: #333;
	margin-right: 16rpx;
}

.category-tag {
	padding: 8rpx 16rpx;
	border-radius: 16rpx;
	color: #ffffff;
	font-size: 20rpx;
}

.stats-row {
	display: flex;
	margin-bottom: 24rpx;
}

.stat-item {
	font-size: 24rpx;
	color: #666;
	margin-right: 32rpx;
}

.rating-row {
	display: flex;
	align-items: center;
	margin-bottom: 32rpx;
}

.stars {
	margin-right: 16rpx;
}

.star {
	font-size: 24rpx;
	color: #ddd;
}

.star.filled {
	color: #ffd700;
}

.rating-text {
	font-size: 24rpx;
	color: #666;
}

.author-row {
	display: flex;
	align-items: center;
	padding-top: 24rpx;
	border-top: 1rpx solid #f0f0f0;
}

.author-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	margin-right: 24rpx;
}

.author-info {
	flex: 1;
}

.author-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 4rpx;
}

.author-desc {
	font-size: 24rpx;
	color: #666;
}

.follow-btn {
	padding: 16rpx 32rpx;
	background-color: #007aff;
	color: #ffffff;
	border: none;
	border-radius: 16rpx;
	font-size: 24rpx;
}

.follow-btn.followed {
	background-color: #f0f0f0;
	color: #666;
}

.follow-btn::after {
	border: none;
}

.card-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 24rpx;
}

.desc-content {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
	margin-bottom: 32rpx;
}

.features-title, .tags-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 16rpx;
}

.features-list {
	margin-bottom: 32rpx;
}

.feature-item {
	display: block;
	font-size: 24rpx;
	color: #666;
	line-height: 1.8;
}

.tags-list {
	display: flex;
	flex-wrap: wrap;
}

.tag {
	display: inline-block;
	background-color: #f0f0f0;
	color: #666;
	padding: 8rpx 16rpx;
	border-radius: 16rpx;
	font-size: 20rpx;
	margin-right: 16rpx;
	margin-bottom: 16rpx;
}

.file-info {
	
}

.info-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.info-row:last-child {
	border-bottom: none;
}

.info-label {
	font-size: 28rpx;
	color: #666;
}

.info-value {
	font-size: 28rpx;
	color: #333;
	text-align: right;
	flex: 1;
	margin-left: 32rpx;
}

.link-text {
	color: #007aff !important;
	text-decoration: underline;
}

.related-list {
	
}

.related-item {
	display: flex;
	align-items: center;
	padding: 24rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.related-item:last-child {
	border-bottom: none;
}

.related-image {
	width: 96rpx;
	height: 96rpx;
	border-radius: 16rpx;
	margin-right: 24rpx;
}

.related-info {
	flex: 1;
}

.related-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 8rpx;
}

.related-desc {
	font-size: 24rpx;
	color: #666;
}

.related-arrow {
	font-size: 32rpx;
	color: #ccc;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #ffffff;
	padding: 32rpx;
	border-top: 1rpx solid #f0f0f0;
	display: flex;
	gap: 24rpx;
}

.action-btn {
	flex: 1;
	height: 88rpx;
	border: none;
	border-radius: 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	font-weight: 600;
}

.action-btn::after {
	border: none;
}

.action-btn.secondary {
	background-color: #f0f0f0;
	color: #666;
}

.action-btn.primary {
	background-color: #007aff;
	color: #ffffff;
}

.btn-icon {
	margin-right: 8rpx;
	font-size: 28rpx;
}

.btn-text {
	
}
</style>
